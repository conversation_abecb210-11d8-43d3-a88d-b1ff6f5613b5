apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-service
  namespace: workforce-management
  labels:
    app: customer-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: customer-service
  template:
    metadata:
      labels:
        app: customer-service
    spec:
      containers:
      - name: customer-service
        image: microservice_with_kubernetes-customer-service:latest
        imagePullPolicy: Never  # Use local Docker image
        ports:
        - containerPort: 8081
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_PROFILES_ACTIVE
        - name: SPRING_DATASOURCE_URL
          value: "******************************************"
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-password
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_JPA_HIBERNATE_DDL_AUTO
        - name: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
        - name: SPRING_SQL_INIT_MODE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_SQL_INIT_MODE
        - name: SERVER_PORT
          value: "8081"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: customer-service
  namespace: workforce-management
  labels:
    app: customer-service
spec:
  selector:
    app: customer-service
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
  type: ClusterIP
