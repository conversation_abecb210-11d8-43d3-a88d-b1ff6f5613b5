apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-contract-service
  namespace: workforce-management
  labels:
    app: customer-contract-service
    module: labor-hiring-contract
spec:
  replicas: 3  # Tăng số replicas cho module quan trọng
  selector:
    matchLabels:
      app: customer-contract-service
  template:
    metadata:
      labels:
        app: customer-contract-service
        module: labor-hiring-contract
    spec:
      containers:
      - name: customer-contract-service
        image: microservice_with_kubernetes-customer-contract-service:latest
        imagePullPolicy: Never  # Use local Docker image
        ports:
        - containerPort: 8083
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_PROFILES_ACTIVE
        - name: SPRING_DATASOURCE_URL
          value: "**************************************************"
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-password
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_JPA_HIBERNATE_DDL_AUTO
        - name: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
        - name: SPRING_SQL_INIT_MODE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_SQL_INIT_MODE
        - name: SERVER_PORT
          value: "8083"
        - name: CUSTOMER_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: CUSTOMER_SERVICE_URL
        - name: JOB_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: JOB_SERVICE_URL
        - name: JOB_CATEGORY_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: JOB_CATEGORY_SERVICE_URL
        resources:
          requests:
            memory: "768Mi"  # Tăng memory cho module quan trọng
            cpu: "500m"
          limits:
            memory: "1.5Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8083
          initialDelaySeconds: 90  # Tăng thời gian khởi động
          periodSeconds: 30
          timeoutSeconds: 15
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8083
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: customer-contract-service
  namespace: workforce-management
  labels:
    app: customer-contract-service
    module: labor-hiring-contract
spec:
  selector:
    app: customer-contract-service
  ports:
  - port: 8083
    targetPort: 8083
    protocol: TCP
  type: ClusterIP
