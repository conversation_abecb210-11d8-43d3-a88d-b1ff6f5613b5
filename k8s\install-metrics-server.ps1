# PowerShell script to install Kubernetes Metrics Server for HPA

Write-Host "=== INSTALLING KUBERNETES METRICS SERVER ===" -ForegroundColor Green
Write-Host "Required for Horizontal Pod Autoscaler (HPA) functionality" -ForegroundColor Yellow
Write-Host ""

# Check if metrics server is already installed
Write-Host "Checking if metrics server is already installed..." -ForegroundColor Yellow
$metricsServer = kubectl get deployment metrics-server -n kube-system 2>$null

if ($metricsServer) {
    Write-Host "✓ Metrics server is already installed" -ForegroundColor Green
    kubectl get deployment metrics-server -n kube-system
} else {
    Write-Host "Installing metrics server..." -ForegroundColor Yellow
    
    # Install metrics server for Docker Desktop Kubernetes
    kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Metrics server installed successfully" -ForegroundColor Green
        
        # Patch metrics server for Docker Desktop (disable TLS verification)
        Write-Host "Patching metrics server for Docker Desktop..." -ForegroundColor Yellow
        kubectl patch deployment metrics-server -n kube-system --type='json' -p='[
            {
                "op": "add",
                "path": "/spec/template/spec/containers/0/args/-",
                "value": "--kubelet-insecure-tls"
            }
        ]'
        
        Write-Host "Waiting for metrics server to be ready..." -ForegroundColor Yellow
        kubectl wait --for=condition=available deployment/metrics-server -n kube-system --timeout=300s
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Metrics server is ready!" -ForegroundColor Green
        } else {
            Write-Host "⚠ Metrics server may take a few more minutes to be ready" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ Failed to install metrics server" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "Testing metrics server..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test metrics
kubectl top nodes 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Metrics server is working correctly" -ForegroundColor Green
    Write-Host ""
    Write-Host "Node metrics:" -ForegroundColor Cyan
    kubectl top nodes
    Write-Host ""
    Write-Host "Pod metrics for workforce-management:" -ForegroundColor Cyan
    kubectl top pods -n workforce-management
} else {
    Write-Host "⚠ Metrics server is still starting up. Please wait a few minutes and try again." -ForegroundColor Yellow
    Write-Host "You can check status with: kubectl get pods -n kube-system | grep metrics-server" -ForegroundColor White
}

Write-Host ""
Write-Host "After metrics server is ready, HPA will start working automatically." -ForegroundColor Green
Write-Host "Check HPA status with: kubectl get hpa -n workforce-management" -ForegroundColor White
