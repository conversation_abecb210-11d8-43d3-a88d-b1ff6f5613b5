# PowerShell script to access Kubernetes services via port-forwarding

Write-Host "=== WORKFORCE MANAGEMENT SYSTEM - SERVICE ACCESS ===" -ForegroundColor Green
Write-Host "Labor Hiring Contract Module - Kubernetes Deployment" -ForegroundColor Yellow
Write-Host ""

function Show-Menu {
    Write-Host "Available Services:" -ForegroundColor Cyan
    Write-Host "1. Frontend (React App) - Port 3000" -ForegroundColor White
    Write-Host "2. API Gateway - Port 8080" -ForegroundColor White
    Write-Host "3. Customer Contract Service (Main Module) - Port 8083" -ForegroundColor Yellow
    Write-Host "4. Customer Service - Port 8081" -ForegroundColor White
    Write-Host "5. Job Service - Port 8082" -ForegroundColor White
    Write-Host "6. Customer Payment Service - Port 8084" -ForegroundColor White
    Write-Host "7. Customer Statistics Service - Port 8085" -ForegroundColor White
    Write-Host "8. PostgreSQL Database - Port 5432" -ForegroundColor White
    Write-Host "9. Show All Pods Status" -ForegroundColor Cyan
    Write-Host "10. Show HPA Status" -ForegroundColor Cyan
    Write-Host "0. Exit" -ForegroundColor Red
    Write-Host ""
}

function Start-PortForward {
    param(
        [string]$ServiceName,
        [string]$Port,
        [string]$DisplayName
    )
    
    Write-Host "Starting port-forward for $DisplayName..." -ForegroundColor Yellow
    Write-Host "Access URL: http://localhost:$Port" -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop port-forwarding" -ForegroundColor Yellow
    Write-Host ""
    
    kubectl port-forward svc/$ServiceName ${Port}:${Port} -n workforce-management
}

function Show-PodsStatus {
    Write-Host "Current Pods Status:" -ForegroundColor Cyan
    kubectl get pods -n workforce-management
    Write-Host ""
    
    Write-Host "Contract Service Pods (Main Module):" -ForegroundColor Yellow
    kubectl get pods -l app=customer-contract-service -n workforce-management
    Write-Host ""
}

function Show-HPAStatus {
    Write-Host "Horizontal Pod Autoscaler Status:" -ForegroundColor Cyan
    kubectl get hpa -n workforce-management
    Write-Host ""
    
    Write-Host "Contract Service HPA Details:" -ForegroundColor Yellow
    kubectl describe hpa customer-contract-service-hpa -n workforce-management
    Write-Host ""
}

# Main loop
while ($true) {
    Show-Menu
    $choice = Read-Host "Select a service (0-10)"
    
    switch ($choice) {
        "1" {
            Start-PortForward "frontend" "3000" "Frontend (React App)"
        }
        "2" {
            Start-PortForward "api-gateway" "8080" "API Gateway"
        }
        "3" {
            Write-Host "🎯 LABOR HIRING CONTRACT MODULE (Main Service)" -ForegroundColor Yellow
            Start-PortForward "customer-contract-service" "8083" "Customer Contract Service"
        }
        "4" {
            Start-PortForward "customer-service" "8081" "Customer Service"
        }
        "5" {
            Start-PortForward "job-service" "8082" "Job Service"
        }
        "6" {
            Start-PortForward "customer-payment-service" "8084" "Customer Payment Service"
        }
        "7" {
            Start-PortForward "customer-statistics-service" "8085" "Customer Statistics Service"
        }
        "8" {
            Start-PortForward "postgres" "5432" "PostgreSQL Database"
        }
        "9" {
            Show-PodsStatus
        }
        "10" {
            Show-HPAStatus
        }
        "0" {
            Write-Host "Goodbye!" -ForegroundColor Green
            exit 0
        }
        default {
            Write-Host "Invalid choice. Please select 0-10." -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Press any key to continue..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    Clear-Host
}
