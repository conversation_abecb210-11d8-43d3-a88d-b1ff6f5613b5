# PowerShell script to manage Workforce Management System
# Supports both Docker Compose and Kubernet<PERSON> deployments

Write-Host "=== WORKFORCE MANAGEMENT SYSTEM - DEPLOYMENT MANAGER ===" -ForegroundColor Green
Write-Host "Labor Hiring Contract Module - Multi-Platform Support" -ForegroundColor Yellow
Write-Host ""

function Show-MainMenu {
    Write-Host "🚀 DEPLOYMENT OPTIONS:" -ForegroundColor Cyan
    Write-Host "1. Docker Compose Deployment" -ForegroundColor White
    Write-Host "2. Kubernetes Deployment" -ForegroundColor White
    Write-Host "3. System Status Check" -ForegroundColor White
    Write-Host "4. Access Services" -ForegroundColor White
    Write-Host "5. View Documentation" -ForegroundColor White
    Write-Host "0. Exit" -ForegroundColor Red
    Write-Host ""
}

function Show-DockerMenu {
    Write-Host "🐳 DOCKER COMPOSE OPTIONS:" -ForegroundColor Cyan
    Write-Host "1. Build and Deploy All Services" -ForegroundColor White
    Write-Host "2. Stop All Services" -ForegroundColor White
    Write-Host "3. View Service Status" -ForegroundColor White
    Write-Host "4. View Service Logs" -ForegroundColor White
    Write-Host "5. Restart Services" -ForegroundColor White
    Write-Host "0. Back to Main Menu" -ForegroundColor Yellow
    Write-Host ""
}

function Show-KubernetesMenu {
    Write-Host "☸️ KUBERNETES OPTIONS:" -ForegroundColor Cyan
    Write-Host "1. Deploy to Kubernetes" -ForegroundColor White
    Write-Host "2. Access Services (Port Forward)" -ForegroundColor White
    Write-Host "3. Install Metrics Server" -ForegroundColor White
    Write-Host "4. View Pod Status" -ForegroundColor White
    Write-Host "5. View HPA Status" -ForegroundColor White
    Write-Host "6. Scale Contract Service" -ForegroundColor White
    Write-Host "7. Cleanup Deployment" -ForegroundColor Red
    Write-Host "0. Back to Main Menu" -ForegroundColor Yellow
    Write-Host ""
}

function Handle-DockerCompose {
    while ($true) {
        Show-DockerMenu
        $choice = Read-Host "Select option (0-5)"
        
        switch ($choice) {
            "1" {
                Write-Host "🔨 Building and deploying with Docker Compose..." -ForegroundColor Yellow
                docker-compose down
                docker-compose build
                docker-compose up -d
                Write-Host "✅ Docker Compose deployment completed!" -ForegroundColor Green
                docker-compose ps
            }
            "2" {
                Write-Host "🛑 Stopping all Docker services..." -ForegroundColor Yellow
                docker-compose down
                Write-Host "✅ All services stopped!" -ForegroundColor Green
            }
            "3" {
                Write-Host "📊 Docker Compose Service Status:" -ForegroundColor Cyan
                docker-compose ps
            }
            "4" {
                $service = Read-Host "Enter service name (or 'all' for all services)"
                if ($service -eq "all") {
                    docker-compose logs
                } else {
                    docker-compose logs $service
                }
            }
            "5" {
                Write-Host "🔄 Restarting all services..." -ForegroundColor Yellow
                docker-compose restart
                Write-Host "✅ Services restarted!" -ForegroundColor Green
            }
            "0" { return }
            default {
                Write-Host "❌ Invalid choice. Please select 0-5." -ForegroundColor Red
            }
        }
        Write-Host ""
        Read-Host "Press Enter to continue"
        Clear-Host
    }
}

function Handle-Kubernetes {
    while ($true) {
        Show-KubernetesMenu
        $choice = Read-Host "Select option (0-7)"
        
        switch ($choice) {
            "1" {
                Write-Host "☸️ Deploying to Kubernetes..." -ForegroundColor Yellow
                Set-Location k8s
                
                # Check if kubectl is available
                try {
                    kubectl version --client | Out-Null
                    Write-Host "✅ kubectl is available" -ForegroundColor Green
                } catch {
                    Write-Host "❌ kubectl not found. Please install kubectl first." -ForegroundColor Red
                    Set-Location ..
                    continue
                }
                
                # Deploy manifests
                $manifests = @(
                    "00-namespace.yaml",
                    "01-postgresql.yaml",
                    "01-database-init.yaml",
                    "02-configmaps.yaml",
                    "03-customer-service.yaml",
                    "04-job-service.yaml",
                    "05-customer-contract-service.yaml",
                    "06-customer-payment-service.yaml",
                    "07-customer-statistics-service.yaml",
                    "08-api-gateway.yaml",
                    "09-frontend.yaml",
                    "10-hpa.yaml"
                )
                
                foreach ($manifest in $manifests) {
                    Write-Host "Applying $manifest..." -ForegroundColor Yellow
                    kubectl apply -f $manifest
                    Start-Sleep -Seconds 2
                }
                
                Write-Host "✅ Kubernetes deployment completed!" -ForegroundColor Green
                Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow
                kubectl wait --for=condition=available deployment/customer-contract-service -n workforce-management --timeout=300s
                
                Set-Location ..
            }
            "2" {
                Write-Host "🌐 Starting access services script..." -ForegroundColor Yellow
                Set-Location k8s
                .\access-services.ps1
                Set-Location ..
            }
            "3" {
                Write-Host "📊 Installing Metrics Server..." -ForegroundColor Yellow
                Set-Location k8s
                .\install-metrics-server.ps1
                Set-Location ..
            }
            "4" {
                Write-Host "📋 Kubernetes Pod Status:" -ForegroundColor Cyan
                kubectl get pods -n workforce-management
                Write-Host ""
                Write-Host "🎯 Contract Service Pods:" -ForegroundColor Yellow
                kubectl get pods -l app=customer-contract-service -n workforce-management
            }
            "5" {
                Write-Host "📈 HPA Status:" -ForegroundColor Cyan
                kubectl get hpa -n workforce-management
                Write-Host ""
                kubectl describe hpa customer-contract-service-hpa -n workforce-management
            }
            "6" {
                $replicas = Read-Host "Enter number of replicas for Contract Service (3-10)"
                if ($replicas -match '^\d+$' -and [int]$replicas -ge 3 -and [int]$replicas -le 10) {
                    kubectl scale deployment customer-contract-service --replicas=$replicas -n workforce-management
                    Write-Host "✅ Contract Service scaled to $replicas replicas!" -ForegroundColor Green
                } else {
                    Write-Host "❌ Invalid number. Please enter 3-10." -ForegroundColor Red
                }
            }
            "7" {
                $confirm = Read-Host "⚠️ This will delete all Kubernetes resources. Are you sure? (y/N)"
                if ($confirm -eq "y" -or $confirm -eq "Y") {
                    Set-Location k8s
                    .\cleanup.ps1
                    Set-Location ..
                }
            }
            "0" { return }
            default {
                Write-Host "❌ Invalid choice. Please select 0-7." -ForegroundColor Red
            }
        }
        Write-Host ""
        Read-Host "Press Enter to continue"
        Clear-Host
    }
}

function Show-SystemStatus {
    Write-Host "🔍 SYSTEM STATUS CHECK:" -ForegroundColor Cyan
    Write-Host ""
    
    # Check Docker
    Write-Host "🐳 Docker Status:" -ForegroundColor Yellow
    try {
        docker --version
        Write-Host "✅ Docker is available" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "Docker Compose Services:" -ForegroundColor Cyan
        docker-compose ps 2>$null
    } catch {
        Write-Host "❌ Docker not available" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Check Kubernetes
    Write-Host "☸️ Kubernetes Status:" -ForegroundColor Yellow
    try {
        kubectl version --client --short
        Write-Host "✅ kubectl is available" -ForegroundColor Green
        
        try {
            kubectl cluster-info --request-timeout=5s | Out-Null
            Write-Host "✅ Kubernetes cluster is accessible" -ForegroundColor Green
            
            Write-Host ""
            Write-Host "Kubernetes Deployments:" -ForegroundColor Cyan
            kubectl get deployments -n workforce-management 2>$null
        } catch {
            Write-Host "❌ Kubernetes cluster not accessible" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ kubectl not available" -ForegroundColor Red
    }
}

function Show-Documentation {
    Write-Host "📚 DOCUMENTATION:" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Available Documentation Files:" -ForegroundColor Yellow
    Write-Host "1. k8s/README.md - Kubernetes Deployment Guide" -ForegroundColor White
    Write-Host "2. k8s/DEPLOYMENT_SUCCESS_REPORT.md - Deployment Report" -ForegroundColor White
    Write-Host "3. DOCKER_DEPLOYMENT_GUIDE.md - Docker Guide" -ForegroundColor White
    Write-Host ""
    
    $choice = Read-Host "Enter number to view documentation (1-3) or press Enter to skip"
    
    switch ($choice) {
        "1" {
            if (Test-Path "k8s/README.md") {
                Get-Content "k8s/README.md" | More
            }
        }
        "2" {
            if (Test-Path "k8s/DEPLOYMENT_SUCCESS_REPORT.md") {
                Get-Content "k8s/DEPLOYMENT_SUCCESS_REPORT.md" | More
            }
        }
        "3" {
            if (Test-Path "DOCKER_DEPLOYMENT_GUIDE.md") {
                Get-Content "DOCKER_DEPLOYMENT_GUIDE.md" | More
            }
        }
    }
}

# Main execution loop
while ($true) {
    Show-MainMenu
    $choice = Read-Host "Select deployment option (0-5)"
    
    switch ($choice) {
        "1" {
            Clear-Host
            Handle-DockerCompose
        }
        "2" {
            Clear-Host
            Handle-Kubernetes
        }
        "3" {
            Clear-Host
            Show-SystemStatus
        }
        "4" {
            Write-Host "🌐 Choose platform:" -ForegroundColor Cyan
            Write-Host "1. Docker Compose Services" -ForegroundColor White
            Write-Host "2. Kubernetes Services" -ForegroundColor White
            $platform = Read-Host "Select platform (1-2)"
            
            if ($platform -eq "2") {
                Set-Location k8s
                .\access-services.ps1
                Set-Location ..
            } else {
                Write-Host "Docker Compose services accessible at:" -ForegroundColor Green
                Write-Host "  Frontend: http://localhost:3000" -ForegroundColor White
                Write-Host "  API Gateway: http://localhost:8080" -ForegroundColor White
                Write-Host "  Contract Service: http://localhost:8083" -ForegroundColor White
            }
        }
        "5" {
            Clear-Host
            Show-Documentation
        }
        "0" {
            Write-Host "👋 Thank you for using Workforce Management System!" -ForegroundColor Green
            exit 0
        }
        default {
            Write-Host "❌ Invalid choice. Please select 0-5." -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Read-Host "Press Enter to continue"
    Clear-Host
}
