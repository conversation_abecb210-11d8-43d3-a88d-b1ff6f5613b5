apiVersion: v1
kind: Namespace
metadata:
  name: workforce-management
  labels:
    name: workforce-management
    app: workforce-management-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: workforce-management
data:
  POSTGRES_DB: postgres
  POSTGRES_USER: postgres
  POSTGRES_PASSWORD: "1234"
  PGDATA: /var/lib/postgresql/data/pgdata
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: workforce-management
type: Opaque
data:
  # postgres:1234 base64 encoded
  username: cG9zdGdyZXM=
  password: MTIzNA==
