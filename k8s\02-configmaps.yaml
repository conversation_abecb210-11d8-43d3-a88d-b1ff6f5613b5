apiVersion: v1
kind: ConfigMap
metadata:
  name: microservices-config
  namespace: workforce-management
data:
  # Database Configuration
  POSTGRES_HOST: "postgres"
  POSTGRES_PORT: "5432"
  POSTGRES_USERNAME: "postgres"
  POSTGRES_PASSWORD: "1234"
  
  # Spring Boot Configuration
  SPRING_PROFILES_ACTIVE: "kubernetes"
  SPRING_JPA_HIBERNATE_DDL_AUTO: "update"
  SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING: "true"
  SPRING_SQL_INIT_MODE: "never"
  
  # Service URLs for inter-service communication
  CUSTOMER_SERVICE_URL: "http://customer-service:8081/api/customer"
  JOB_SERVICE_URL: "http://job-service:8082/api/job"
  JOB_CATEGORY_SERVICE_URL: "http://job-service:8082/api/job-category"
  CUSTOMERCONTRACT_SERVICE_URL: "http://customer-contract-service:8083/api/customer-contract"
  CUSTOMER_PAYMENT_SERVICE_URL: "http://customer-payment-service:8084/api/customer-payment"
  
  # Customer Statistics Service URLs
  APP_CUSTOMER_SERVICE_URL: "http://customer-service:8081/api/customer"
  APP_CUSTOMER_CONTRACT_SERVICE_URL: "http://customer-contract-service:8083/api/customer-contract"
  APP_CUSTOMER_PAYMENT_SERVICE_URL: "http://customer-payment-service:8084/api/customer-payment"
---
apiVersion: v1
kind: Secret
metadata:
  name: microservices-secret
  namespace: workforce-management
type: Opaque
data:
  # Database credentials (base64 encoded)
  db-username: cG9zdGdyZXM=  # postgres
  db-password: MTIzNA==      # 1234
