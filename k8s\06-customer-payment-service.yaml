apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-payment-service
  namespace: workforce-management
  labels:
    app: customer-payment-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: customer-payment-service
  template:
    metadata:
      labels:
        app: customer-payment-service
    spec:
      containers:
      - name: customer-payment-service
        image: microservice_with_kubernetes-customer-payment-service:latest
        imagePullPolicy: Never  # Use local Docker image
        ports:
        - containerPort: 8084
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_PROFILES_ACTIVE
        - name: SPRING_DATASOURCE_URL
          value: "*************************************************"
        - name: SPRING_DATASOURCE_USERNAME
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-username
        - name: SPRING_DATASOURCE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: microservices-secret
              key: db-password
        - name: SPRING_JPA_HIBERNATE_DDL_AUTO
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_JPA_HIBERNATE_DDL_AUTO
        - name: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
        - name: SPRING_SQL_INIT_MODE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_SQL_INIT_MODE
        - name: SERVER_PORT
          value: "8084"
        - name: CUSTOMER_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: CUSTOMER_SERVICE_URL
        - name: CUSTOMERCONTRACT_SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: CUSTOMERCONTRACT_SERVICE_URL
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8084
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8084
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: customer-payment-service
  namespace: workforce-management
  labels:
    app: customer-payment-service
spec:
  selector:
    app: customer-payment-service
  ports:
  - port: 8084
    targetPort: 8084
    protocol: TCP
  type: ClusterIP
