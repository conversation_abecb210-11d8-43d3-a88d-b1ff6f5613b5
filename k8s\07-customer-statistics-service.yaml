apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-statistics-service
  namespace: workforce-management
  labels:
    app: customer-statistics-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: customer-statistics-service
  template:
    metadata:
      labels:
        app: customer-statistics-service
    spec:
      containers:
      - name: customer-statistics-service
        image: microservice_with_kubernetes-customer-statistics-service:latest
        imagePullPolicy: Never  # Use local Docker image
        ports:
        - containerPort: 8085
        env:
        - name: SPRING_PROFILES_ACTIVE
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_PROFILES_ACTIVE
        - name: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: SPRING_MAIN_ALLOW_BEAN_DEFINITION_OVERRIDING
        - name: SERVER_PORT
          value: "8085"
        - name: APP_CUSTOMER-SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: APP_CUSTOMER_SERVICE_URL
        - name: APP_CUSTOMER-CONTRACT-SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: APP_CUSTOMER_CONTRACT_SERVICE_URL
        - name: APP_CUSTOMER-PAYMENT-SERVICE_URL
          valueFrom:
            configMapKeyRef:
              name: microservices-config
              key: APP_CUSTOMER_PAYMENT_SERVICE_URL
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "400m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8085
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: customer-statistics-service
  namespace: workforce-management
  labels:
    app: customer-statistics-service
spec:
  selector:
    app: customer-statistics-service
  ports:
  - port: 8085
    targetPort: 8085
    protocol: TCP
  type: ClusterIP
