# PowerShell script to deploy microservices to Kubernetes
# Focus on Labor Hiring Contract module

Write-Host "=== KUBERNETES DEPLOYMENT - WORKFORCE MANAGEMENT SYSTEM ===" -ForegroundColor Green
Write-Host "Focus: Labor Hiring Contract Module" -ForegroundColor Yellow
Write-Host ""

# Function to check if kubectl is available
function Test-Kubectl {
    try {
        kubectl version --client --short | Out-Null
        return $true
    } catch {
        Write-Host "✗ kubectl is not installed or not in PATH" -ForegroundColor Red
        return $false
    }
}

# Function to check if Kubernetes cluster is accessible
function Test-KubernetesCluster {
    try {
        kubectl cluster-info | Out-Null
        return $true
    } catch {
        Write-Host "✗ Cannot connect to Kubernetes cluster" -ForegroundColor Red
        return $false
    }
}

# Check prerequisites
Write-Host "1. CHECKING PREREQUISITES..." -ForegroundColor Cyan

if (-not (Test-Kubectl)) {
    Write-Host "Please install kubectl and ensure it's in your PATH" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-KubernetesCluster)) {
    Write-Host "Please ensure your Kubernetes cluster is running and accessible" -ForegroundColor Yellow
    Write-Host "For local development, you can use:" -ForegroundColor White
    Write-Host "  - Docker Desktop with Kubernetes enabled" -ForegroundColor White
    Write-Host "  - Minikube" -ForegroundColor White
    Write-Host "  - Kind" -ForegroundColor White
    exit 1
}

Write-Host "✓ kubectl is available" -ForegroundColor Green
Write-Host "✓ Kubernetes cluster is accessible" -ForegroundColor Green

# Deploy in order
Write-Host ""
Write-Host "2. DEPLOYING KUBERNETES MANIFESTS..." -ForegroundColor Cyan

$manifests = @(
    "00-namespace.yaml",
    "01-postgresql.yaml", 
    "02-configmaps.yaml",
    "03-customer-service.yaml",
    "04-job-service.yaml",
    "05-customer-contract-service.yaml",  # Main contract module
    "06-customer-payment-service.yaml",
    "07-customer-statistics-service.yaml",
    "08-api-gateway.yaml",
    "09-frontend.yaml",
    "10-hpa.yaml"
)

foreach ($manifest in $manifests) {
    Write-Host "Deploying $manifest..." -ForegroundColor Yellow
    kubectl apply -f $manifest
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ $manifest deployed successfully" -ForegroundColor Green
    } else {
        Write-Host "✗ Failed to deploy $manifest" -ForegroundColor Red
        exit 1
    }
    
    # Wait a bit between deployments
    Start-Sleep -Seconds 2
}

Write-Host ""
Write-Host "3. WAITING FOR DEPLOYMENTS TO BE READY..." -ForegroundColor Cyan

# Wait for PostgreSQL to be ready first
Write-Host "Waiting for PostgreSQL..." -ForegroundColor Yellow
kubectl wait --for=condition=ready pod -l app=postgres -n workforce-management --timeout=300s

# Wait for core services
$services = @("customer-service", "job-service", "customer-contract-service")
foreach ($service in $services) {
    Write-Host "Waiting for $service..." -ForegroundColor Yellow
    kubectl wait --for=condition=available deployment/$service -n workforce-management --timeout=300s
}

# Wait for remaining services
$remainingServices = @("customer-payment-service", "customer-statistics-service", "api-gateway", "frontend")
foreach ($service in $remainingServices) {
    Write-Host "Waiting for $service..." -ForegroundColor Yellow
    kubectl wait --for=condition=available deployment/$service -n workforce-management --timeout=300s
}

Write-Host ""
Write-Host "4. DEPLOYMENT STATUS..." -ForegroundColor Cyan

# Show deployment status
kubectl get deployments -n workforce-management
Write-Host ""
kubectl get services -n workforce-management
Write-Host ""
kubectl get pods -n workforce-management

Write-Host ""
Write-Host "🎉 KUBERNETES DEPLOYMENT COMPLETED!" -ForegroundColor Green
Write-Host ""
Write-Host "LABOR HIRING CONTRACT MODULE STATUS:" -ForegroundColor Cyan
kubectl get deployment customer-contract-service -n workforce-management
Write-Host ""

Write-Host "ACCESS INFORMATION:" -ForegroundColor Cyan
Write-Host "  API Gateway: kubectl port-forward svc/api-gateway 8080:8080 -n workforce-management" -ForegroundColor White
Write-Host "  Frontend: kubectl port-forward svc/frontend 3000:3000 -n workforce-management" -ForegroundColor White
Write-Host "  Contract Service: kubectl port-forward svc/customer-contract-service 8083:8083 -n workforce-management" -ForegroundColor White
Write-Host ""
Write-Host "MONITORING COMMANDS:" -ForegroundColor Cyan
Write-Host "  View logs: kubectl logs -f deployment/customer-contract-service -n workforce-management" -ForegroundColor White
Write-Host "  Check HPA: kubectl get hpa -n workforce-management" -ForegroundColor White
Write-Host "  Scale manually: kubectl scale deployment customer-contract-service --replicas=5 -n workforce-management" -ForegroundColor White
