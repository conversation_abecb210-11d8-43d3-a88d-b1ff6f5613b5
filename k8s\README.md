# Kubernetes Deployment - Workforce Management System

## Overview
This directory contains Kubernetes manifests for deploying the Workforce Management System with focus on the **Labor Hiring Contract Module**.

## Architecture

### Core Services
- **PostgreSQL Database** - Persistent data storage
- **Customer Service** - Customer management
- **Job Service** - Job categories and management
- **Customer Contract Service** - **Main contract module** (3 replicas with HPA)
- **Customer Payment Service** - Payment processing
- **Customer Statistics Service** - Revenue analytics
- **API Gateway** - Central routing and load balancing
- **Frontend** - React application

### Key Features for Contract Module
- **High Availability**: 3 replicas minimum for contract service
- **Auto Scaling**: HPA configured for CPU/Memory based scaling
- **Health Checks**: Comprehensive liveness and readiness probes
- **Resource Management**: Optimized CPU and memory allocation
- **Service Discovery**: Internal service communication via DNS

## Prerequisites

1. **Kubernetes Cluster**
   - Docker Desktop with Kubernetes enabled, OR
   - Minikube, OR
   - Kind, OR
   - Cloud Kubernetes service (AKS, EKS, GKE)

2. **kubectl** installed and configured

3. **Docker Images** built locally:
   ```bash
   docker-compose build
   ```

## Deployment

### Quick Deploy
```powershell
cd k8s
.\deploy.ps1
```

### Manual Deploy
```bash
# Apply manifests in order
kubectl apply -f 00-namespace.yaml
kubectl apply -f 01-postgresql.yaml
kubectl apply -f 02-configmaps.yaml
kubectl apply -f 03-customer-service.yaml
kubectl apply -f 04-job-service.yaml
kubectl apply -f 05-customer-contract-service.yaml  # Main contract module
kubectl apply -f 06-customer-payment-service.yaml
kubectl apply -f 07-customer-statistics-service.yaml
kubectl apply -f 08-api-gateway.yaml
kubectl apply -f 09-frontend.yaml
kubectl apply -f 10-hpa.yaml
```

## Access Applications

### Port Forwarding (Local Development)
```bash
# API Gateway
kubectl port-forward svc/api-gateway 8080:8080 -n workforce-management

# Frontend
kubectl port-forward svc/frontend 3000:3000 -n workforce-management

# Contract Service (Direct Access)
kubectl port-forward svc/customer-contract-service 8083:8083 -n workforce-management
```

### Ingress (Production)
Add these entries to your `/etc/hosts` file:
```
127.0.0.1 workforce-api.local
127.0.0.1 workforce-app.local
```

Then access:
- Frontend: http://workforce-app.local
- API: http://workforce-api.local

## Monitoring & Management

### Check Deployment Status
```bash
kubectl get all -n workforce-management
```

### View Contract Service Logs
```bash
kubectl logs -f deployment/customer-contract-service -n workforce-management
```

### Check Auto Scaling
```bash
kubectl get hpa -n workforce-management
```

### Scale Contract Service Manually
```bash
kubectl scale deployment customer-contract-service --replicas=5 -n workforce-management
```

### Database Access
```bash
kubectl exec -it statefulset/postgres -n workforce-management -- psql -U postgres
```

## Contract Module Specific Features

### High Availability Configuration
- **3 replicas minimum** for contract service
- **Load balancing** across multiple pods
- **Rolling updates** with zero downtime
- **Health checks** every 10 seconds

### Auto Scaling Rules
- **CPU threshold**: 70%
- **Memory threshold**: 80%
- **Scale up**: Max 100% increase every 15 seconds
- **Scale down**: Max 10% decrease every 60 seconds
- **Max replicas**: 10 pods

### Resource Allocation
- **Requests**: 768Mi memory, 500m CPU
- **Limits**: 1.5Gi memory, 1000m CPU
- **Optimized** for contract processing workloads

## Troubleshooting

### Common Issues

1. **Pods not starting**
   ```bash
   kubectl describe pod <pod-name> -n workforce-management
   ```

2. **Service not accessible**
   ```bash
   kubectl get svc -n workforce-management
   kubectl describe svc <service-name> -n workforce-management
   ```

3. **Database connection issues**
   ```bash
   kubectl logs deployment/customer-contract-service -n workforce-management
   ```

### Cleanup
```powershell
cd k8s
.\cleanup.ps1
```

## Production Considerations

1. **Persistent Volumes**: Configure proper storage classes
2. **Secrets Management**: Use external secret management
3. **Network Policies**: Implement security policies
4. **Resource Quotas**: Set namespace resource limits
5. **Monitoring**: Deploy Prometheus/Grafana
6. **Logging**: Configure centralized logging
7. **Backup**: Implement database backup strategy
