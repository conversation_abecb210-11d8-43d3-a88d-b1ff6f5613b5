# PowerShell script to cleanup Kubernetes deployment

Write-Host "=== KUBERNETES CLEANUP - WORKFORCE MANAGEMENT SYSTEM ===" -ForegroundColor Red
Write-Host ""

$confirmation = Read-Host "Are you sure you want to delete all resources? (y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "Cleanup cancelled." -ForegroundColor Yellow
    exit 0
}

Write-Host "Cleaning up Kubernetes resources..." -ForegroundColor Yellow

# Delete all resources in the namespace
kubectl delete namespace workforce-management

Write-Host "✓ All resources cleaned up successfully!" -ForegroundColor Green
