apiVersion: v1
kind: ConfigMap
metadata:
  name: database-init-script
  namespace: workforce-management
data:
  init-databases.sql: |
    -- Create databases for microservices
    CREATE DATABASE IF NOT EXISTS customerdb;
    CREATE DATABASE IF NOT EXISTS jobdb;
    CREATE DATABASE IF NOT EXISTS customercontractdb;
    CREATE DATABASE IF NOT EXISTS customerpaymentdb;
    
    -- Grant permissions
    GRANT ALL PRIVILEGES ON DATABASE customerdb TO postgres;
    GRANT ALL PRIVILEGES ON DATABASE jobdb TO postgres;
    GRANT ALL PRIVILEGES ON DATABASE customercontractdb TO postgres;
    GRANT ALL PRIVILEGES ON DATABASE customerpaymentdb TO postgres;
---
apiVersion: batch/v1
kind: Job
metadata:
  name: database-init
  namespace: workforce-management
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: database-init
        image: postgres:15-alpine
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgres -p 5432 -U postgres; do
            echo "PostgreSQL is not ready yet..."
            sleep 2
          done
          echo "PostgreSQL is ready. Creating databases..."
          
          # Create databases
          PGPASSWORD=1234 psql -h postgres -U postgres -c "CREATE DATABASE customerdb;" || echo "customerdb already exists"
          PGPASSWORD=1234 psql -h postgres -U postgres -c "CREATE DATABASE jobdb;" || echo "jobdb already exists"
          PGPASSWORD=1234 psql -h postgres -U postgres -c "CREATE DATABASE customercontractdb;" || echo "customercontractdb already exists"
          PGPASSWORD=1234 psql -h postgres -U postgres -c "CREATE DATABASE customerpaymentdb;" || echo "customerpaymentdb already exists"
          
          echo "Database initialization completed!"
        env:
        - name: PGPASSWORD
          value: "1234"
      initContainers:
      - name: wait-for-postgres
        image: postgres:15-alpine
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for PostgreSQL to be ready..."
          until pg_isready -h postgres -p 5432 -U postgres; do
            echo "PostgreSQL is not ready yet..."
            sleep 5
          done
          echo "PostgreSQL is ready!"
        env:
        - name: PGPASSWORD
          value: "1234"
